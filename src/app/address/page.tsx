import { getInitialMapData } from '@/actions/location.actions';
import AppBar from '@/components/common/app-bar';
import MapContainer from '@/components/location/map-container';
import { getQueryClient } from '@/get-query-client';
import { GET_INITIAL_MAP_DATA_QUERY_KEY } from '@/queries/location.queries';
import ProtectedAddressPage from '@/components/address/protected-address-page';

export default async function LocationPage() {
  const queryClient = getQueryClient();

  await queryClient.prefetchQuery({
    queryKey: [GET_INITIAL_MAP_DATA_QUERY_KEY],
    queryFn: async () => await getInitialMapData({}),
  });

  return (
    <ProtectedAddressPage>
      <div className="h-[100dvh] flex flex-col relative">
        <AppBar title={'Set Delivery Location'} />
        {/* the map and the map interface + data handling */}
        <MapContainer action={'set'} />
      </div>
    </ProtectedAddressPage>
  );
}
