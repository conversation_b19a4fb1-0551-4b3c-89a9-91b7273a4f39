import AppBar from '@/components/common/app-bar';
import { getQueryClient } from '@/get-query-client';
import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import MapContainer from '@/components/location/map-container';
import { getInitialMapData } from '@/actions/location.actions';
import { GET_INITIAL_MAP_DATA_QUERY_KEY } from '@/queries/location.queries';
import ProtectedAddressWrapper from '@/components/address/protected-address-wrapper';

interface EditAddressPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function EditAddressPage({ params }: EditAddressPageProps) {
  const queryClient = getQueryClient();
  const resolvedParams = await params;
  const addressId = resolvedParams.id;

  await queryClient.prefetchQuery({
    queryKey: [GET_INITIAL_MAP_DATA_QUERY_KEY, addressId],
    queryFn: async () => await getInitialMapData({ address_id: addressId }),
  });

  return (
    <ProtectedAddressWrapper>
      <HydrationBoundary state={dehydrate(queryClient)}>
        <div className="h-[100dvh] flex flex-col relative">
          <AppBar title={`Edit Address`} />
          {/* the map and the map interface + data handling */}
          <MapContainer action={'edit'} addressId={addressId} />
        </div>
      </HydrationBoundary>
    </ProtectedAddressWrapper>
  );
}
