import AppBar from '@/components/common/app-bar';
import MapContainer from '@/components/location/map-container';
import ProtectedAddressWrapper from '@/components/address/protected-address-wrapper';

export default async function LocationPage() {
  return (
    <ProtectedAddressWrapper>
      <div className="h-[100dvh] flex flex-col relative">
        <AppBar title={'Save a New Address'} />
        {/* the map and the map interface + data handling */}
        <MapContainer action={'new'} />
      </div>
    </ProtectedAddressWrapper>
  );
}
