'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useGlobalDataStore } from '@/store/global-data.store';

/**
 * Hook to protect components/pages that require authentication
 * Redirects to home page if user is not authenticated
 */
export function useAuthProtection() {
  const { globalData, isHydrated } = useGlobalDataStore();
  const router = useRouter();

  useEffect(() => {
    // Only check after store is hydrated to avoid false redirects
    if (isHydrated && !globalData.user) {
      router.replace('/');
    }
  }, [globalData.user, isHydrated, router]);

  // Return whether user is authenticated and store is hydrated
  return {
    isAuthenticated: !!globalData.user,
    isLoading: !isHydrated,
  };
}
