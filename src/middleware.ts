'use server';

import { NextRequest, NextResponse } from 'next/server';
import { generateDeviceId } from './actions/encryption.actions';

export async function middleware(request: NextRequest) {
  const response = NextResponse.next();
  const deviceId = request.cookies.get('X-Device-Id')?.value;
  const refreshToken = request.cookies.get('x-refresh-token')?.value;

  // Check authentication for address add/edit routes only
  if (
    request.nextUrl.pathname.startsWith('/address/add') ||
    request.nextUrl.pathname.startsWith('/address/edit')
  ) {
    if (!refreshToken || refreshToken === 'null') {
      // Redirect to home if no valid refresh token
      return NextResponse.redirect(new URL('/', request.url));
    }
  }

  if (!deviceId) {
    const encryptedDeviceId = await generateDeviceId();
    response.cookies.set('X-Device-Id', encryptedDeviceId, {
      maxAge: 365 * 24 * 60 * 60,
      path: '/',
      secure: process.env.NODE_ENV === 'production',
    });
  }

  return response;
}

// Match all request paths except for the ones starting with:
// - api (API routes)
// - _next/static (static files)
// - _next/image (image optimization files)
// - favicon.ico (favicon file)
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
