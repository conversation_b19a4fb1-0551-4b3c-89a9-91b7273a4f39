/* eslint-disable @typescript-eslint/no-explicit-any */

'use client';

import { useEffect, useState } from 'react';
import RouteSheet from '@/components/common/route-sheet';
import AppBar from '@/components/common/app-bar';
import MapContainer from '@/components/location/map-container';
import { HydrationBoundary, dehydrate } from '@tanstack/react-query';
import { getQueryClient } from '@/get-query-client';
import { getInitialMapData } from '@/actions/location.actions';
import { GET_INITIAL_MAP_DATA_QUERY_KEY } from '@/queries/location.queries';
import { useAuthProtection } from '@/hooks/use-auth-protection';

interface AddressModalWrapperProps {
  action: 'set' | 'new' | 'edit';
  title: string;
  addressId?: string;
}

export default function AddressModalWrapper({
  action,
  title,
  addressId,
}: AddressModalWrapperProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [dehydratedState, setDehydratedState] = useState<any>(null);
  const { isAuthenticated, isLoading: authLoading } = useAuthProtection();

  useEffect(() => {
    const prefetchData = async () => {
      try {
        const queryClient = getQueryClient();

        const queryKey = addressId
          ? [GET_INITIAL_MAP_DATA_QUERY_KEY, addressId]
          : [GET_INITIAL_MAP_DATA_QUERY_KEY];

        await queryClient.prefetchQuery({
          queryKey,
          queryFn: async () => await getInitialMapData(addressId ? { address_id: addressId } : {}),
        });

        setDehydratedState(dehydrate(queryClient));
      } catch (error) {
        console.error('Failed to prefetch map data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    prefetchData();
  }, [addressId]);

  // Don't render anything if authentication is still loading or user is not authenticated
  if (authLoading || !isAuthenticated) {
    return <RouteSheet isLoading={true} />;
  }

  return (
    <RouteSheet isLoading={isLoading}>
      {!isLoading && dehydratedState && (
        <HydrationBoundary state={dehydratedState}>
          <div className="h-full w-375 flex flex-col relative">
            <AppBar title={title} />
            <MapContainer action={action} addressId={addressId} />
          </div>
        </HydrationBoundary>
      )}
    </RouteSheet>
  );
}
