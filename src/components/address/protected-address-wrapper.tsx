'use client';

import { useAuthProtection } from '@/hooks/use-auth-protection';
import Loader from '@/components/common/loader';

interface ProtectedAddressWrapperProps {
  children: React.ReactNode;
}

/**
 * Wrapper component that protects address add/edit pages
 * Shows loader while checking authentication, redirects if not authenticated
 */
export default function ProtectedAddressWrapper({ children }: ProtectedAddressWrapperProps) {
  const { isAuthenticated, isLoading } = useAuthProtection();

  // Show loader while checking authentication
  if (isLoading) {
    return (
      <div className="h-[100dvh] flex flex-col relative">
        <Loader />
      </div>
    );
  }

  // If not authenticated, useAuthProtection hook will handle redirect
  // This component will unmount before reaching here
  if (!isAuthenticated) {
    return (
      <div className="h-[100dvh] flex flex-col relative">
        <Loader />
      </div>
    );
  }

  return <>{children}</>;
}
